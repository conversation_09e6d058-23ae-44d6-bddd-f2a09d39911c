package services

import (
	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	projectDTO "gitlab.finema.co/finema/finework/finework-api/modules/project/dto"
	projectRepositories "gitlab.finema.co/finema/finework/finework-api/modules/project/repositories"
	projectServices "gitlab.finema.co/finema/finework/finework-api/modules/project/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectService interface {
	Create(input *dto.PMOProjectCreatePayload) (*models.PMOProject, core.IError)
	Update(id string, input *dto.PMOProjectUpdatePayload) (*models.PMOProject, core.IError)
	Find(id string) (*models.PMOProject, core.IError)
	FindBySlug(slug string) (*models.PMOProject, core.IError)
	Pagination(pageOptions *core.PageOptions, options *dto.PMOProjectPaginationOptions) (*repository.Pagination[models.PMOProject], core.IError)
	Delete(id string) core.IError
	CheckSlug(slug string) (bool, core.IError)
}

type pmoProjectService struct {
	ctx core.IContext
}

func (s pmoProjectService) Create(input *dto.PMOProjectCreatePayload) (*models.PMOProject, core.IError) {
	pmoProject := &models.PMOProject{
		BaseModel:   models.NewBaseModel(),
		Name:        input.Name,
		Slug:        input.Slug,
		Email:       input.Email,
		Tags:        input.Tags,
		Status:      models.PMOProjectStatusDraft,
		CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	if input.ProjectID != nil {
		pmoProject.ProjectID = utils.ToNonPointer(input.ProjectID)
	} else {
		projectCount, ierr := projectRepositories.Project(s.ctx).Where("name = ? OR code = ? ", input.Name, input.Slug).Count()
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}
		if projectCount > 0 {
			return nil, s.ctx.NewError(emsgs.ProjectAlreadyExists, emsgs.ProjectAlreadyExists)
		}

		projectSvc := projectServices.NewProjectService(s.ctx)
		project, ierr := projectSvc.Create(&projectDTO.ProjectCreatePayload{
			Name: input.Name,
			Code: input.Slug,
		})
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}

		pmoProject.ProjectID = project.ID
	}

	ierr := repositories.PMOProject(s.ctx).Create(pmoProject)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// create checklist items from template
	checkListItems, ierr := repositories.PMOTemplateChecklistItem(s.ctx).FindAll()
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	if len(checkListItems) > 0 {
		checklistItems := make([]models.PMOChecklistItem, 0)
		for _, item := range checkListItems {
			checklistItem := models.PMOChecklistItem{
				BaseModel:   models.NewBaseModel(),
				ProjectID:   pmoProject.ID,
				TabKey:      item.TabKey,
				Detail:      item.Detail,
				CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
				UpdatedByID: utils.ToPointer(s.ctx.GetUser().ID),
			}
			checklistItems = append(checklistItems, checklistItem)
		}

		ierr = repositories.PMOChecklistItem(s.ctx).Create(checklistItems)
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}
	}

	return s.Find(pmoProject.ID)
}

func (s pmoProjectService) Update(id string, input *dto.PMOProjectUpdatePayload) (*models.PMOProject, core.IError) {
	pmoProject, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.Name != "" {
		pmoProject.Name = input.Name
	}
	if input.Slug != "" {
		pmoProject.Slug = input.Slug
	}
	if input.Email != "" {
		pmoProject.Email = input.Email
	}
	if input.Tags != nil {
		pmoProject.Tags = input.Tags
	}
	if input.Status != "" {
		pmoProject.Status = input.Status
	}
	if input.ProjectID != "" {
		pmoProject.ProjectID = input.ProjectID
	}

	pmoProject.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)

	ierr = repositories.PMOProject(s.ctx).Where("id = ?", id).Updates(pmoProject)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(pmoProject.ID)
}

func (s pmoProjectService) Find(id string) (*models.PMOProject, core.IError) {
	return repositories.PMOProject(s.ctx,
		repositories.PMOProjectWithRelations(false),
		repositories.PMOProjectByOwnerPermission(s.ctx.GetUser().ID),
		repositories.PMOProjectWithCurrentPermission(s.ctx.GetUser().ID),
	).FindOne("id = ?", id)
}

func (s pmoProjectService) FindBySlug(slug string) (*models.PMOProject, core.IError) {
	return repositories.PMOProject(s.ctx,
		repositories.PMOProjectWithRelations(false),
		repositories.PMOProjectByOwnerPermission(s.ctx.GetUser().ID),
		repositories.PMOProjectWithCurrentPermission(s.ctx.GetUser().ID),
	).FindOne("slug = ?", slug)
}

func (s pmoProjectService) Pagination(pageOptions *core.PageOptions, options *dto.PMOProjectPaginationOptions) (*repository.Pagination[models.PMOProject], core.IError) {
	return repositories.PMOProject(s.ctx,
		repositories.PMOProjectOrderBy(pageOptions),
		repositories.PMOProjectWithRelations(utils.ToNonPointer(options.IncludeAll)),
		repositories.PMOProjectWithSearch(pageOptions.Q),
		repositories.PMOProjectWithStatus(utils.ToNonPointer(options.Status)),
		repositories.PMOProjectWithCurrentPermission(s.ctx.GetUser().ID),
		repositories.PMOProjectByOwnerPermission(s.ctx.GetUser().ID),
	).Pagination(pageOptions)
}

func (s pmoProjectService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOProject(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func (s pmoProjectService) CheckSlug(slug string) (bool, core.IError) {
	count, ierr := repositories.PMOProject(s.ctx).Where("slug = ?", slug).Count()
	if ierr != nil {
		return false, s.ctx.NewError(ierr, ierr)
	}

	if count == 0 {
		return true, nil
	}

	return false, nil
}

func NewPMOProjectService(ctx core.IContext) IPMOProjectService {
	return &pmoProjectService{ctx: ctx}
}
