# PMO Collaborator Main Person Constraint

## Overview

This document describes the implementation of the constraint that ensures only one person can be marked as "main" for each tab within a PMO project.

## Problem Statement

Previously, the PMO collaborator system allowed multiple people to be marked as "main" for the same tab within a project, which could lead to confusion about who is the primary responsible person for each tab.

## Solution

The system now automatically enforces the constraint that only one person can be marked as "main" for each tab within a project. When a new person is set as "main" for a tab, any previously assigned "main" person for that tab is automatically unset.

## Implementation Details

### Modified Files

1. **models/pmo_collaborator.model.go**
   - Added `BizcoPermission` and `BizcoMain` fields to match the database schema

2. **modules/pmo/dto/pmo_project.dto.go**
   - Added `BizcoPermission` and `BizcoMain` fields to both Create and Update payloads

3. **modules/pmo/requests/pmo_collaborator_create.request.go**
   - Added `BizcoPermission` and `BizcoMain` fields
   - Added validation for `BizcoPermission`

4. **modules/pmo/requests/pmo_collaborator_update.request.go**
   - Added `BizcoPermission` and `BizcoMain` fields
   - Added validation for `BizcoPermission`

5. **modules/pmo/handlers/pmo_project_collaborator.controller.go**
   - Updated Create and Update methods to handle Bizco fields

6. **modules/pmo/services/pmo_project_collaborator.service.go**
   - Added `handleMainPersonConstraints()` method
   - Added `unsetPreviousMainPerson()` helper method
   - Updated Create and Update methods to call constraint handling
   - Added support for all tab types including Bizco

### Core Logic

#### handleMainPersonConstraints Method

This method checks each tab type and ensures only one person can be marked as "main":

```go
func (s pmoProjectCollaboratorService) handleMainPersonConstraints(
    projectID, excludeCollaboratorID string, 
    confidentialMain, salesMain, presalesMain, biddingMain, pmoMain, bizcoMain *bool
) core.IError
```

For each tab where a person is being set as "main" (value is `true`), it calls `unsetPreviousMainPerson()` to remove the "main" flag from other collaborators.

#### unsetPreviousMainPerson Method

This helper method unsets the main flag for other collaborators in the same project and tab:

```go
func (s pmoProjectCollaboratorService) unsetPreviousMainPerson(
    projectID, excludeCollaboratorID, mainField string
) core.IError
```

It updates the database to set `main_field = false` for all other collaborators in the same project, excluding the current collaborator being updated.

### Supported Tab Types

The constraint is enforced for all tab types:

1. **Confidential** - `confidential_main`
2. **Sales** - `sales_main`
3. **Presales** - `presales_main`
4. **Bidding** - `bidding_main`
5. **PMO** - `pmo_main`
6. **Bizco** - `bizco_main`

### Behavior

#### Create Operation
1. When creating a new collaborator, if any `*_main` field is set to `true`
2. The system first unsets any existing "main" person for that tab
3. Then creates the new collaborator with the "main" flag

#### Update Operation
1. When updating an existing collaborator, if any `*_main` field is being set to `true`
2. The system first unsets any existing "main" person for that tab (excluding the current collaborator)
3. Then updates the collaborator with the new "main" flag

#### Multiple Tabs
- A single person can be "main" for multiple different tabs
- But only one person can be "main" for each specific tab within a project

## Testing

Use the provided test file `test_main_person_constraint.http` to verify the constraint is working correctly:

1. Create multiple collaborators with the same tab marked as "main"
2. Verify only the last one remains as "main"
3. Update existing collaborators to change "main" assignments
4. Verify the constraint is maintained

## Database Impact

The implementation uses database updates to maintain consistency:
- Uses `WHERE` clauses to target specific project and tab combinations
- Excludes the current collaborator when unsetting previous "main" assignments
- Includes `deleted_at IS NULL` to only affect active collaborators

## Error Handling

The constraint handling is integrated into the existing error handling system:
- Database errors during constraint enforcement are propagated up
- Transaction-like behavior ensures consistency
- Failed constraint handling prevents the main operation from completing
