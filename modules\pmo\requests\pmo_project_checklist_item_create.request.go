package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOProjectChecklistItemCreate struct {
	core.BaseValidator
	TabKey     *string `json:"tab_key"`
	Detail     *string `json:"detail"`
	IsChecked  *bool   `json:"is_checked"`
	AssigneeID *string `json:"assignee_id"`
}

func (r *PMOProjectChecklistItemCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Tab<PERSON>ey, "tab_key"))
	r.Must(r.IsStrRequired(r.Detail, "detail"))

	r.Must(r.Is<PERSON>trIn(r.<PERSON>b<PERSON>, strings.Join(models.PMOTabKeys, "|"), "tab_key"))
	r.Must(r.IsExists(ctx, r.<PERSON>signee<PERSON>, models.User{}.TableName(), "id", "assignee_id"))

	return r.<PERSON>rror()
}
