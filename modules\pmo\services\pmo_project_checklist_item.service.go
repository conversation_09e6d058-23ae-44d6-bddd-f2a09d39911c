package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectChecklistItemService interface {
	Create(input *dto.PMOProjectChecklistItemCreatePayload) (*models.PMOChecklistItem, core.IError)
	Update(id string, input *dto.PMOProjectChecklistItemUpdatePayload) (*models.PMOChecklistItem, core.IError)
	Find(id string) (*models.PMOChecklistItem, core.IError)
	Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOProjectChecklistItemPaginationOptions) (*repository.Pagination[models.PMOChecklistItem], core.IError)
	Delete(id string) core.IError
}

type pmoProjectChecklistItemService struct {
	ctx core.IContext
}

// PMO Project Checklist Item methods implementation
func (s pmoProjectChecklistItemService) Create(input *dto.PMOProjectChecklistItemCreatePayload) (*models.PMOChecklistItem, core.IError) {
	checklistItem := &models.PMOChecklistItem{
		BaseModel:   models.NewBaseModel(),
		ProjectID:   input.ProjectID,
		TabKey:      models.PMOTabKey(input.TabKey),
		Detail:      input.Detail,
		IsChecked:   input.IsChecked,
		AssigneeID:  input.AssigneeID,
		CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repositories.PMOChecklistItem(s.ctx).Create(checklistItem)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(checklistItem.ID)
}

func (s pmoProjectChecklistItemService) Update(id string, input *dto.PMOProjectChecklistItemUpdatePayload) (*models.PMOChecklistItem, core.IError) {
	checklistItem, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	mapUpdate := map[string]interface{}{
		"updated_by_id": s.ctx.GetUser().ID,
	}

	if input.Detail != nil {
		mapUpdate["detail"] = input.Detail
	}
	if input.IsChecked != nil {
		mapUpdate["is_checked"] = input.IsChecked
	}
	if input.AssigneeID != nil {
		mapUpdate["assignee_id"] = input.AssigneeID
	}

	ierr = repositories.PMOChecklistItem(s.ctx).Where("id = ?", id).Updates(mapUpdate)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(checklistItem.ID)
}

func (s pmoProjectChecklistItemService) Find(id string) (*models.PMOChecklistItem, core.IError) {
	return repositories.PMOChecklistItem(s.ctx,
		repositories.PMOChecklistItemWithRelations(),
	).FindOne("id = ?", id)
}

func (s pmoProjectChecklistItemService) Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOProjectChecklistItemPaginationOptions) (*repository.Pagination[models.PMOChecklistItem], core.IError) {
	return repositories.PMOChecklistItem(s.ctx,
		repositories.PMOChecklistItemOrderBy(pageOptions),
		repositories.PMOChecklistItemWithRelations(),
		repositories.PMOChecklistItemByProjectID(projectID),
		repositories.PMOChecklistItemWithTabKeyFilter(options.TabKey),
		repositories.PMOChecklistItemWithIsCheckedFilter(options.IsChecked),
		repositories.PMOChecklistItemWithSearch(pageOptions.Q),
	).Pagination(pageOptions)
}

func (s pmoProjectChecklistItemService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOChecklistItem(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func NewPMOProjectChecklistItemService(ctx core.IContext) IPMOProjectChecklistItemService {
	return &pmoProjectChecklistItemService{ctx: ctx}
}
