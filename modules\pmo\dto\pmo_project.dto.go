package dto

import (
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

type PMOProjectCreatePayload struct {
	Name      string   `json:"name"`
	Slug      string   `json:"slug"`
	Email     string   `json:"email"`
	Tags      []string `json:"tags"`
	ProjectID *string  `json:"project_id"`
}

type PMOProjectUpdatePayload struct {
	Name      string                  `json:"name"`
	Slug      string                  `json:"slug"`
	Email     string                  `json:"email"`
	Tags      []string                `json:"tags"`
	Status    models.PMOProjectStatus `json:"status"`
	ProjectID string                  `json:"project_id"`
}

type PMOProjectPaginationOptions struct {
	Status     *string `json:"status"`
	TabKey     *string `json:"tab_key"`
	IncludeAll *bool   `json:"include_all"`
}

// PMO Document Group DTOs
type PMODocumentGroupCreatePayload struct {
	ProjectID     string `json:"project_id"`
	Tab<PERSON>ey        string `json:"tab_key"`
	GroupName     string `json:"group_name"`
	SharepointURL string `json:"sharepoint_url"`
}

type PMODocumentGroupUpdatePayload struct {
	TabKey        string `json:"tab_key"`
	GroupName     string `json:"group_name"`
	SharepointURL string `json:"sharepoint_url"`
}

type PMODocumentGroupPaginationOptions struct {
	ProjectID *string `json:"project_id"`
	TabKey    *string `json:"tab_key"`
}

// PMO Document Item DTOs
type PMODocumentItemCreatePayload struct {
	ProjectID     string     `json:"project_id"`
	TabKey        string     `json:"tab_key"`
	GroupID       string     `json:"group_id"`
	Name          string     `json:"name"`
	SharepointURL string     `json:"sharepoint_url"`
	Date          *time.Time `json:"date"`
	Type          string     `json:"type"`
	FileID        *string    `json:"file_id"`
}

type PMODocumentItemUpdatePayload struct {
	Name          string     `json:"name"`
	SharepointURL string     `json:"sharepoint_url"`
	Date          *time.Time `json:"date"`
	Type          string     `json:"type"`
	FileID        *string    `json:"file_id"`
}

type PMODocumentItemPaginationOptions struct {
	ProjectID *string `json:"project_id"`
	GroupID   *string `json:"group_id"`
	TabKey    *string `json:"tab_key"`
	Type      *string `json:"type"`
}

// PMO Comment DTOs
type PMOCommentCreatePayload struct {
	ProjectID       string                   `json:"project_id"`
	Channel         models.PMOCommentChannel `json:"channel"`
	Detail          string                   `json:"detail"`
	IsClientFlag    bool                     `json:"is_client_flag"`
	ParentCommentID *string                  `json:"parent_comment_id"`
}

type PMOCommentUpdatePayload struct {
	Detail       string `json:"detail"`
	IsClientFlag *bool  `json:"is_client_flag"`
}

type PMOCommentPaginationOptions struct {
	Channel  *string
	ParentID *string `json:"parent_id"`
}

// PMO Collaborator DTOs
type PMOCollaboratorCreatePayload struct {
	ProjectID              string                   `json:"project_id"`
	UserID                 string                   `json:"user_id"`
	ConfidentialPermission *models.PMOTabPermission `json:"confidential_permission"`
	ConfidentialMain       *bool                    `json:"confidential_main"`
	SalesPermission        *models.PMOTabPermission `json:"sales_permission"`
	SalesMain              *bool                    `json:"sales_main"`
	PresalesPermission     *models.PMOTabPermission `json:"presales_permission"`
	PresalesMain           *bool                    `json:"presales_main"`
	BiddingPermission      *models.PMOTabPermission `json:"bidding_permission"`
	BiddingMain            *bool                    `json:"bidding_main"`
	PMOPermission          *models.PMOTabPermission `json:"pmo_permission"`
	PMOMain                *bool                    `json:"pmo_main"`
}

type PMOCollaboratorUpdatePayload struct {
	ConfidentialPermission *models.PMOTabPermission `json:"confidential_permission"`
	ConfidentialMain       *bool                    `json:"confidential_main"`
	SalesPermission        *models.PMOTabPermission `json:"sales_permission"`
	SalesMain              *bool                    `json:"sales_main"`
	PresalesPermission     *models.PMOTabPermission `json:"presales_permission"`
	PresalesMain           *bool                    `json:"presales_main"`
	BiddingPermission      *models.PMOTabPermission `json:"bidding_permission"`
	BiddingMain            *bool                    `json:"bidding_main"`
	PMOPermission          *models.PMOTabPermission `json:"pmo_permission"`
	PMOMain                *bool                    `json:"pmo_main"`
}

type PMOCollaboratorPaginationOptions struct {
	TabKey *string `json:"tab_key"`
}

// PMO Remark DTOs
type PMORemarkCreatePayload struct {
	ProjectID string           `json:"project_id"`
	TabKey    models.PMOTabKey `json:"tab_key"`
	Detail    string           `json:"detail"`
}

type PMORemarkUpdatePayload struct {
	Detail string `json:"detail"`
}

type PMORemarkPaginationOptions struct {
	ProjectID *string `json:"project_id"`
	TabKey    *string `json:"tab_key"`
}

// PMO Contact DTOs
type PMOContactCreatePayload struct {
	ProjectID string `json:"project_id"`
	Fullname  string `json:"fullname"`
	Phone     string `json:"phone"`
	Email     string `json:"email"`
	Detail    string `json:"detail"`
	Company   string `json:"company"`
	Position  string `json:"position"`
}

type PMOContactUpdatePayload struct {
	Fullname string `json:"fullname"`
	Phone    string `json:"phone"`
	Email    string `json:"email"`
	Detail   string `json:"detail"`
	Company  string `json:"company"`
	Position string `json:"position"`
}

type PMOContactPaginationOptions struct {
	ProjectID *string `json:"project_id"`
}

// PMO Competitor DTOs
type PMOCompetitorCreatePayload struct {
	ProjectID string `json:"project_id"`
	Company   string `json:"company"`
	Detail    string `json:"detail"`
}

type PMOCompetitorUpdatePayload struct {
	Company string `json:"company"`
	Detail  string `json:"detail"`
}

type PMOCompetitorPaginationOptions struct {
	ProjectID *string `json:"project_id"`
}

// PMO Partner DTOs
type PMOPartnerCreatePayload struct {
	ProjectID string `json:"project_id"`
	Company   string `json:"company"`
	Detail    string `json:"detail"`
}

type PMOPartnerUpdatePayload struct {
	Company string `json:"company"`
	Detail  string `json:"detail"`
}

type PMOPartnerPaginationOptions struct {
	ProjectID *string `json:"project_id"`
}

// PMO Budget DTOs
type PMOBudgetCreatePayload struct {
	ProjectID    string  `json:"project_id"`
	FundType     string  `json:"fund_type"`
	ProjectValue float64 `json:"project_value"`
	BidbondValue float64 `json:"bidbond_value"`
	Partner      string  `json:"partner"`
}

// PMO Bidding DTOs
type PMOBiddingCreatePayload struct {
	ProjectID    string     `json:"project_id"`
	BiddingType  string     `json:"bidding_type"`
	BiddingValue float64    `json:"bidding_value"`
	TenderDate   *time.Time `json:"tender_date"`
	TenderEntity string     `json:"tender_entity"`
	AnnounceDate *time.Time `json:"announce_date"`
}

// PMO Bidbond DTOs
type PMOBidbondCreatePayload struct {
	ProjectID      string     `json:"project_id"`
	GuaranteeAsset string     `json:"guarantee_asset"`
	BidbondPayer   string     `json:"bidbond_payer"`
	BidbondValue   float64    `json:"bidbond_value"`
	StartDate      *time.Time `json:"start_date"`
	EndDate        *time.Time `json:"end_date"`
	DurationMonth  int64      `json:"duration_month"`
	DurationYear   int64      `json:"duration_year"`
	Fee            float64    `json:"fee"`
}

// PMO Contract DTOs
type PMOContractCreatePayload struct {
	ProjectID            string     `json:"project_id"`
	ContractNo           string     `json:"contract_no"`
	Value                float64    `json:"value"`
	SigningDate          *time.Time `json:"signing_date"`
	StartDate            *time.Time `json:"start_date"`
	EndDate              *time.Time `json:"end_date"`
	DurationDay          int64      `json:"duration_day"`
	WarrantyDurationDay  int64      `json:"warranty_duration_day"`
	WarrantyDurationYear int64      `json:"warranty_duration_year"`
	Prime                string     `json:"prime"`
	PenaltyFee           float64    `json:"penalty_fee"`
	IsLegalizeStamp      bool       `json:"is_legalize_stamp"`
}

// PMO LG DTOs
type PMOLGCreatePayload struct {
	ProjectID string     `json:"project_id"`
	Value     float64    `json:"value"`
	StartDate *time.Time `json:"start_date"`
	EndDate   *time.Time `json:"end_date"`
	Fee       float64    `json:"fee"`
	Interest  float64    `json:"interest"`
}

// PMO Vendor Item DTOs
type PMOVendorItemCreatePayload struct {
	ProjectID          string `json:"project_id"`
	VendorName         string `json:"vendor_name"`
	ItemName           string `json:"item_name"`
	ItemDetail         string `json:"item_detail"`
	DeliverDurationDay int64  `json:"deliver_duration_day"`
	IsTor              bool   `json:"is_tor"`
	IsImplementation   bool   `json:"is_implementation"`
	IsTraining         bool   `json:"is_training"`
	IsUserManual       bool   `json:"is_user_manual"`
}

type PMOVendorItemUpdatePayload struct {
	VendorName         *string `json:"vendor_name"`
	ItemName           *string `json:"item_name"`
	ItemDetail         *string `json:"item_detail"`
	DeliverDurationDay *int64  `json:"deliver_duration_day"`
	IsTor              *bool   `json:"is_tor"`
	IsImplementation   *bool   `json:"is_implementation"`
	IsTraining         *bool   `json:"is_training"`
	IsUserManual       *bool   `json:"is_user_manual"`
}

type PMOVendorItemPaginationOptions struct {
	ProjectID *string `json:"project_id"`
}

// PMO Project Checklist Item DTOs
type PMOProjectChecklistItemCreatePayload struct {
	ProjectID string `json:"project_id"`
	TabKey    string `json:"tab_key"`
	Detail    string `json:"detail"`
	IsChecked bool   `json:"is_checked"`
	AssigneeID *string `json:"assignee_id"`
}


type PMOProjectChecklistItemUpdatePayload struct {
	Detail    *string `json:"detail"`
	IsChecked *bool   `json:"is_checked"`
	AssigneeID *string `json:"assignee_id"`
}

type PMOProjectChecklistItemPaginationOptions struct {
	TabKey    *string `json:"tab_key"`
	IsChecked *bool   `json:"is_checked"`
}
