package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOProjectPaginationRequest struct {
	core.BaseValidator
	Status     *string `json:"status" query:"status"`
	IncludeAll *bool   `json:"include_all" query:"include_all"`
}

func (r *PMOProjectPaginationRequest) Validate(ctx core.IContext) core.IError {
	r.Must(r.IsStrIn(r.Status, strings.Join(models.PMOProjectStatuses, "|"), "status"))

	return r.Error()
}
