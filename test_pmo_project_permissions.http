### PMO Project Permission Testing
### This file tests the PMOProjectByOwnerPermission implementation

@baseUrl = http://localhost:8080
@token = your-auth-token-here

### 1. Test PMO Projects Pagination (should only show projects user has access to)
### This will test the PMOProjectByOwnerPermission function implementation
GET {{baseUrl}}/pmo/projects
Authorization: Bearer {{token}}
Content-Type: application/json

### 2. Test with search query (should still respect permissions)
GET {{baseUrl}}/pmo/projects?q=test&page=1&limit=10
Authorization: Bearer {{token}}
Content-Type: application/json

### 3. Test with status filter (should still respect permissions)
GET {{baseUrl}}/pmo/projects?status=DRAFT&page=1&limit=10
Authorization: Bearer {{token}}
Content-Type: application/json

### 4. Test with different user tokens to verify permission filtering
### Replace with different user tokens to test different permission levels
GET {{baseUrl}}/pmo/projects
Authorization: Bearer {{token}}
Content-Type: application/json

### Expected Behavior:
### - Users with SUPER or ADMIN PMO access level should see all projects
### - Users with USER PMO access level should only see projects they are collaborators on
### - Users with NONE PMO access level should see no projects (or get access denied)
### - The WHERE clause should filter projects based on:
###   1. User has SUPER/ADMIN access level in user_access_levels table
###   2. OR user is a collaborator with any non-NONE permission in pmo_collaborators table

### Test Cases to Verify:
### 1. Super Admin User: Should see all projects
### 2. Admin User: Should see all projects  
### 3. Regular User with Collaborator Access: Should see only projects they collaborate on
### 4. Regular User without Collaborator Access: Should see no projects
### 5. User with NONE PMO access: Should see no projects

### To test different scenarios:
### 1. Create users with different access levels using the user access level API
### 2. Add users as collaborators to specific projects
### 3. Test the pagination endpoint with different user tokens
### 4. Verify the results match the expected permission logic
