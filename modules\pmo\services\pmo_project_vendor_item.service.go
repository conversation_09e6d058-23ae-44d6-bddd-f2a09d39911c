package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectVendorItemService interface {
	Create(input *dto.PMOVendorItemCreatePayload) (*models.PMOVendorItem, core.IError)
	Update(id string, input *dto.PMOVendorItemUpdatePayload) (*models.PMOVendorItem, core.IError)
	Find(id string) (*models.PMOVendorItem, core.IError)
	Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOVendorItemPaginationOptions) (*repository.Pagination[models.PMOVendorItem], core.IError)
	Delete(id string) core.IError
}

type pmoProjectVendorItemService struct {
	ctx core.IContext
}

// PMO Vendor Item methods implementation
func (s pmoProjectVendorItemService) Create(input *dto.PMOVendorItemCreatePayload) (*models.PMOVendorItem, core.IError) {
	vendorItem := &models.PMOVendorItem{
		BaseModel:          models.NewBaseModel(),
		ProjectID:          input.ProjectID,
		VendorName:         input.VendorName,
		ItemName:           input.ItemName,
		ItemDetail:         input.ItemDetail,
		DeliverDurationDay: input.DeliverDurationDay,
		IsTor:              input.IsTor,
		IsImplementation:   input.IsImplementation,
		IsTraining:         input.IsTraining,
		IsUserManual:       input.IsUserManual,
		CreatedByID:        utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repositories.PMOVendorItem(s.ctx).Create(vendorItem)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(vendorItem.ID)
}

func (s pmoProjectVendorItemService) Update(id string, input *dto.PMOVendorItemUpdatePayload) (*models.PMOVendorItem, core.IError) {
	_, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	mapUpdates := map[string]interface{}{
		"updated_by_id":        s.ctx.GetUser().ID,
		"updated_at":           utils.GetCurrentDateTime(),
	}

	if input.VendorName != nil {
		mapUpdates["vendor_name"] = input.VendorName
	}
	if input.ItemName != nil {
		mapUpdates["item_name"] = input.ItemName
	}
	if input.ItemDetail != nil {
		mapUpdates["item_detail"] = input.ItemDetail
	}
	if input.DeliverDurationDay != nil {
		mapUpdates["deliver_duration_day"] = input.DeliverDurationDay
	}
	if input.IsTor != nil {
		mapUpdates["is_tor"] = input.IsTor
	}
	if input.IsImplementation != nil {
		mapUpdates["is_implementation"] = input.IsImplementation
	}
	if input.IsTraining != nil {
		mapUpdates["is_training"] = input.IsTraining
	}
	if input.IsUserManual != nil {
		mapUpdates["is_user_manual"] = input.IsUserManual
	}

	ierr = repositories.PMOVendorItem(s.ctx).Where("id = ?", id).Updates(mapUpdates)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(id)
}

func (s pmoProjectVendorItemService) Find(id string) (*models.PMOVendorItem, core.IError) {
	return repositories.PMOVendorItem(s.ctx,
		repositories.PMOVendorItemWithRelations(),
	).FindOne("id = ?", id)
}

func (s pmoProjectVendorItemService) Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOVendorItemPaginationOptions) (*repository.Pagination[models.PMOVendorItem], core.IError) {
	return repositories.PMOVendorItem(s.ctx,
		repositories.PMOVendorItemOrderBy(pageOptions),
		repositories.PMOVendorItemWithRelations(),
		repositories.PMOVendorItemByProjectID(projectID),
	).Pagination(pageOptions)
}

func (s pmoProjectVendorItemService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOVendorItem(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func NewPMOProjectVendorItemService(ctx core.IContext) IPMOProjectVendorItemService {
	return &pmoProjectVendorItemService{ctx: ctx}
}
