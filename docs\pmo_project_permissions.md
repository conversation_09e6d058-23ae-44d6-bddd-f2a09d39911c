# PMO Project Permissions Implementation

## Overview

This document describes the implementation of the `PMOProjectByOwnerPermission` function in the PMO project repository, which provides row-level security for PMO projects based on user permissions.

## Implementation Details

### Function: `PMOProjectByOwnerPermission(userID string)`

**Location:** `modules/pmo/repositories/pmo_project.repo.go`

**Purpose:** Filters PMO projects to only show projects that a user has permission to access.

### Permission Logic

The function implements a two-tier permission system:

1. **System-Level Access**: Users with `SUPER` or `ADMIN` access level for PMO can see all projects
2. **Project-Level Access**: Users who are collaborators on specific projects can see those projects

### SQL Query Logic

```sql
SELECT p.id FROM pmo_projects p
WHERE EXISTS (
    -- Check if user has SUPER or ADMIN access level
    SELECT 1 FROM user_access_levels ual 
    WHERE ual.user_id = ? 
    AND ual.pmo IN ('SUPER', 'ADMIN')
)
OR EXISTS (
    -- Check if user is a collaborator with any permissions
    SELECT 1 FROM pmo_collaborators pc 
    WHERE pc.project_id = p.id 
    AND pc.user_id = ?
    AND (
        pc.confidential_permission != 'NONE' OR
        pc.sales_permission != 'NONE' OR
        pc.presales_permission != 'NONE' OR
        pc.bidding_permission != 'NONE' OR
        pc.pmo_permission != 'NONE' OR
        pc.bizco_permission != 'NONE'
    )
)
```

### Access Levels

#### System Access Levels (user_access_levels.pmo)
- `SUPER`: Full access to all PMO projects and system settings
- `ADMIN`: Full access to all PMO projects
- `USER`: Access only to projects where user is a collaborator
- `NONE`: No access to PMO functionality

#### Project Collaboration Permissions (pmo_collaborators)
- `MODIFY`: Can view and modify the tab content
- `READONLY`: Can view the tab content
- `NONE`: No access to the tab

#### PMO Tabs
- `CONFIDENTIAL`: Confidential information
- `SALES`: Sales information  
- `PRESALES`: Presales information
- `BIDDING`: Bidding information
- `PMO`: PMO progress, hardware/software, warranty, legal
- `BIZCO`: Business coordination

## Usage

The function is automatically applied in the `PMOProjectService.Pagination` method:

```go
func (s pmoProjectService) Pagination(pageOptions *core.PageOptions, options *dto.PMOProjectPaginationOptions) (*repository.Pagination[models.PMOProject], core.IError) {
    return repositories.PMOProject(s.ctx,
        repositories.PMOProjectOrderBy(pageOptions),
        repositories.PMOProjectWithRelations(),
        repositories.PMOProjectWithSearch(pageOptions.Q),
        repositories.PMOProjectWithStatus(utils.ToNonPointer(options.Status)),
        repositories.PMOProjectWithCurrentPermission(s.ctx.GetUser().ID),
        repositories.PMOProjectByOwnerPermission(s.ctx.GetUser().ID), // Applied here
    ).Pagination(pageOptions)
}
```

## Security Benefits

1. **Row-Level Security**: Users can only see projects they have permission to access
2. **Principle of Least Privilege**: Users only get access to what they need
3. **Hierarchical Permissions**: System admins can see everything, regular users see only their projects
4. **Granular Control**: Different permission levels for different project aspects

## Testing

Use the provided test file `test_pmo_project_permissions.http` to verify the implementation:

1. Test with different user access levels
2. Test with users who are collaborators on specific projects
3. Verify that users only see projects they should have access to
4. Confirm that search and filtering still work with permission restrictions

## Migration Considerations

This implementation adds a security layer that may reduce the number of projects visible to existing users. Ensure that:

1. Users have appropriate access levels set in `user_access_levels`
2. Collaborator relationships are properly established in `pmo_collaborators`
3. Existing workflows account for the new permission restrictions
