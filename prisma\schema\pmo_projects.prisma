enum PMOProjectStatus {
  DRAFT
  TOR
  BIDDING
  PMO
  WARRANTY
  CLOSED
  CANCEL
}

enum PMOTab<PERSON>ey {
  INFO
  CONFIDENTIAL
  SALES
  PRESALES
  BIDDING
  PMO
  BIZCO
}

model pmo_projects {
  id         String           @id @default(uuid()) @db.Uuid
  name       String           @unique
  slug       String           @unique
  email      String
  tags       String[]         @default([])
  status     PMOProjectStatus @default(DRAFT)
  project_id String           @db.Uuid

  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  project                    projects                     @relation(fields: [project_id], references: [id], onDelete: Cascade)
  collaborators              pmo_collaborators[]
  comments                   pmo_comments[]
  pmo_comment_versions       pmo_comment_versions[]
  pmo_remarks                pmo_remarks[]
  pmo_remark_versions        pmo_remark_versions[]
  pmo_document_groups        pmo_document_groups[]
  pmo_document_items         pmo_document_items[]
  pmo_document_item_versions pmo_document_item_versions[]
  pmo_contacts               pmo_contacts[]
  pmo_competitors            pmo_competitors[]
  pmo_partners               pmo_partners[]
  pmo_budget_info            pmo_budget_info[]
  pmo_budget_info_versions   pmo_budget_info_versions[]
  pmo_bidding_info           pmo_bidding_info[]
  pmo_bidding_info_versions  pmo_bidding_info_versions[]
  pmo_contract_info          pmo_contract_info[]
  pmo_contract_info_versions pmo_contract_info_versions[]
  pmo_bidbond_info           pmo_bidbond_info[]
  pmo_bidbond_info_versions  pmo_bidbond_info_versions[]
  pmo_lg_info                pmo_lg_info[]
  pmo_lg_info_versions       pmo_lg_info_versions[]
  pmo_vendor_items           pmo_vendor_items[]
  pmo_checklist_items        pmo_checklist_items[]

  @@index([name, slug, created_by_id])
}

enum PMOTabPermission {
  READONLY
  MODIFY
  NONE
}

model pmo_collaborators {
  id                      String           @id @default(uuid()) @db.Uuid
  project_id              String           @db.Uuid
  user_id                 String           @db.Uuid
  confidential_permission PMOTabPermission @default(NONE)
  confidential_main       Boolean          @default(false)
  sales_permission        PMOTabPermission @default(NONE)
  sales_main              Boolean          @default(false)
  presales_permission     PMOTabPermission @default(NONE)
  presales_main           Boolean          @default(false)
  bidding_permission      PMOTabPermission @default(NONE)
  bidding_main            Boolean          @default(false)
  pmo_permission          PMOTabPermission @default(NONE)
  pmo_main                Boolean          @default(false)
  bizco_permission        PMOTabPermission @default(NONE)
  bizco_main              Boolean          @default(false)

  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  project pmo_projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
  user    users        @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([project_id, user_id, confidential_permission, confidential_main, sales_permission, sales_main, presales_permission, presales_main, bidding_permission, bidding_main, pmo_permission, pmo_main, bizco_permission, bizco_main, deleted_at])
}

enum PMOCommentChannel {
  OVERALL
  CONFIDENTIAL
  SALES
  PRESALES
  BIDDING
  PMO_PROGRESS
  PMO_HWSW
  PMO_WARRANTY
  PMO_LEGAL
  BIZCO
}

model pmo_comments {
  id                String            @id @default(uuid()) @db.Uuid
  project_id        String            @db.Uuid
  user_id           String            @db.Uuid
  channel           PMOCommentChannel
  detail            String
  is_client_flag    Boolean           @default(false)
  parent_comment_id String?           @db.Uuid
  created_at        DateTime          @default(now())
  created_by_id     String?           @db.Uuid
  updated_at        DateTime          @updatedAt
  updated_by_id     String?           @db.Uuid
  deleted_at        DateTime?
  deleted_by_id     String?           @db.Uuid

  project              pmo_projects           @relation(fields: [project_id], references: [id], onDelete: Cascade)
  user                 users                  @relation(fields: [user_id], references: [id], onDelete: Cascade)
  pmo_comment_versions pmo_comment_versions[]

  @@index([project_id, user_id, channel])
}

model pmo_comment_versions {
  id                String            @id @default(uuid()) @db.Uuid
  comment_id        String            @db.Uuid
  project_id        String            @db.Uuid
  user_id           String            @db.Uuid
  channel           PMOCommentChannel
  detail            String
  is_client_flag    Boolean           @default(false)
  parent_comment_id String?           @db.Uuid
  created_at        DateTime          @default(now())
  created_by_id     String?           @db.Uuid
  updated_at        DateTime          @updatedAt
  updated_by_id     String?           @db.Uuid
  deleted_at        DateTime?
  deleted_by_id     String?           @db.Uuid

  project pmo_projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
  comment pmo_comments @relation(fields: [comment_id], references: [id], onDelete: Cascade)
  user    users        @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([project_id, user_id, channel])
}

model pmo_checklist_items {
  id            String    @id @default(uuid()) @db.Uuid
  project_id    String    @db.Uuid
  tab_key       PMOTabKey
  detail        String
  is_checked    Boolean   @default(false)
  assignee_id   String?   @db.Uuid
  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  project pmo_projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
  assignee users?      @relation(fields: [assignee_id], references: [id], onDelete: SetNull)

  @@index([tab_key])
}

model pmo_remarks {
  id            String    @id @default(uuid()) @db.Uuid
  project_id    String    @db.Uuid
  tab_key       PMOTabKey
  detail        String
  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  project             pmo_projects          @relation(fields: [project_id], references: [id], onDelete: Cascade)
  pmo_remark_versions pmo_remark_versions[]

  @@index([project_id, tab_key])
}

model pmo_remark_versions {
  id            String    @id @default(uuid()) @db.Uuid
  remark_id     String    @db.Uuid
  project_id    String    @db.Uuid
  tab_key       PMOTabKey
  detail        String
  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  project pmo_projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
  remark  pmo_remarks  @relation(fields: [remark_id], references: [id], onDelete: Cascade)

  @@index([project_id, tab_key])
}

model pmo_document_groups {
  id             String    @id @default(uuid()) @db.Uuid
  project_id     String    @db.Uuid
  tab_key        PMOTabKey
  group_name     String
  sharepoint_url String    @default("")
  created_at     DateTime  @default(now())
  created_by_id  String?   @db.Uuid
  updated_at     DateTime  @updatedAt
  updated_by_id  String?   @db.Uuid
  deleted_at     DateTime?
  deleted_by_id  String?   @db.Uuid

  project            pmo_projects         @relation(fields: [project_id], references: [id], onDelete: Cascade)
  pmo_document_items pmo_document_items[]

  @@index([project_id, tab_key])
}

enum PMODocType {
  INBOUND
  OUTBOUND
}

model pmo_document_items {
  id             String     @id @default(uuid()) @db.Uuid
  project_id     String     @db.Uuid
  tab_key        PMOTabKey
  group_id       String     @db.Uuid
  name           String
  sharepoint_url String
  date           DateTime   @db.Date
  type           PMODocType
  file_id        String?    @db.Uuid
  created_at     DateTime   @default(now())
  created_by_id  String?    @db.Uuid
  updated_at     DateTime   @updatedAt
  updated_by_id  String?    @db.Uuid
  deleted_at     DateTime?
  deleted_by_id  String?    @db.Uuid

  project                    pmo_projects                 @relation(fields: [project_id], references: [id], onDelete: Cascade)
  group                      pmo_document_groups          @relation(fields: [group_id], references: [id], onDelete: Cascade)
  file                       files?                       @relation(fields: [file_id], references: [id], onDelete: SetNull)
  pmo_document_item_versions pmo_document_item_versions[]

  @@index([project_id, tab_key, group_id])
}

model pmo_document_item_versions {
  id               String     @id @default(uuid()) @db.Uuid
  document_item_id String     @db.Uuid
  project_id       String     @db.Uuid
  tab_key          PMOTabKey
  group_id         String     @db.Uuid
  name             String
  sharepoint_url   String
  date             DateTime   @db.Date
  type             PMODocType
  file_id          String?    @db.Uuid
  created_at       DateTime   @default(now())
  created_by_id    String?    @db.Uuid
  updated_at       DateTime   @updatedAt
  updated_by_id    String?    @db.Uuid
  deleted_at       DateTime?
  deleted_by_id    String?    @db.Uuid

  project       pmo_projects       @relation(fields: [project_id], references: [id], onDelete: Cascade)
  document_item pmo_document_items @relation(fields: [document_item_id], references: [id], onDelete: Cascade)
  file          files?             @relation(fields: [file_id], references: [id], onDelete: SetNull)

  @@index([project_id, tab_key, group_id])
}

model pmo_contacts {
  id            String    @id @default(uuid()) @db.Uuid
  project_id    String    @db.Uuid
  fullname      String
  phone         String
  email         String
  detail        String
  company       String
  position      String
  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  project pmo_projects @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@index([project_id])
}

model pmo_competitors {
  id            String    @id @default(uuid()) @db.Uuid
  project_id    String    @db.Uuid
  company       String
  detail        String
  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  project pmo_projects @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@index([project_id])
}

model pmo_partners {
  id            String    @id @default(uuid()) @db.Uuid
  project_id    String    @db.Uuid
  company       String
  detail        String
  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  project pmo_projects @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@index([project_id])
}

model pmo_budget_info {
  id            String    @id @default(uuid()) @db.Uuid
  project_id    String    @db.Uuid
  fund_type     String
  project_value Float
  bidbond_value Float
  partner       String
  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  project pmo_projects @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@index([project_id])
}

model pmo_budget_info_versions {
  id             String    @id @default(uuid()) @db.Uuid
  budget_info_id String    @db.Uuid
  project_id     String    @db.Uuid
  fund_type      String
  project_value  Float
  bidbond_value  Float
  partner        String
  created_at     DateTime  @default(now())
  created_by_id  String?   @db.Uuid
  updated_at     DateTime  @updatedAt
  updated_by_id  String?   @db.Uuid
  deleted_at     DateTime?
  deleted_by_id  String?   @db.Uuid

  project pmo_projects @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@index([project_id])
  @@index([budget_info_id])
}

model pmo_bidding_info {
  id            String    @id @default(uuid()) @db.Uuid
  project_id    String    @db.Uuid
  bidding_type  String
  bidding_value Float
  tender_date   DateTime? @db.Date
  tender_entity String
  announce_date DateTime? @db.Date
  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  project                   pmo_projects                @relation(fields: [project_id], references: [id], onDelete: Cascade)
  pmo_bidding_info_versions pmo_bidding_info_versions[]

  @@index([project_id])
}

model pmo_bidding_info_versions {
  id              String    @id @default(uuid()) @db.Uuid
  bidding_info_id String    @db.Uuid
  project_id      String    @db.Uuid
  bidding_type    String
  bidding_value   Float
  tender_date     DateTime? @db.Date
  tender_entity   String
  announce_date   DateTime? @db.Date
  created_at      DateTime  @default(now())
  created_by_id   String?   @db.Uuid
  updated_at      DateTime  @updatedAt
  updated_by_id   String?   @db.Uuid
  deleted_at      DateTime?
  deleted_by_id   String?   @db.Uuid

  project      pmo_projects     @relation(fields: [project_id], references: [id], onDelete: Cascade)
  bidding_info pmo_bidding_info @relation(fields: [bidding_info_id], references: [id], onDelete: Cascade)

  @@index([project_id])
  @@index([bidding_info_id])
}

model pmo_contract_info {
  id                     String    @id @default(uuid()) @db.Uuid
  project_id             String    @db.Uuid
  contract_no            String
  value                  Float
  signing_date           DateTime  @db.Date
  start_date             DateTime  @db.Date
  end_date               DateTime  @db.Date
  duration_day           Int
  warranty_duration_day  Int
  warranty_duration_year Int
  prime                  String
  penalty_fee            Float
  is_legalize_stamp      Boolean
  created_at             DateTime  @default(now())
  created_by_id          String?   @db.Uuid
  updated_at             DateTime  @updatedAt
  updated_by_id          String?   @db.Uuid
  deleted_at             DateTime?
  deleted_by_id          String?   @db.Uuid

  project                    pmo_projects                 @relation(fields: [project_id], references: [id], onDelete: Cascade)
  pmo_contract_info_versions pmo_contract_info_versions[]

  @@index([project_id])
}

model pmo_contract_info_versions {
  id                     String    @id @default(uuid()) @db.Uuid
  contract_info_id       String    @db.Uuid
  project_id             String    @db.Uuid
  contract_no            String
  value                  Float
  signing_date           DateTime  @db.Date
  start_date             DateTime  @db.Date
  end_date               DateTime  @db.Date
  duration_day           Int
  warranty_duration_day  Int
  warranty_duration_year Int
  prime                  String
  penalty_fee            Float
  is_legalize_stamp      Boolean
  created_at             DateTime  @default(now())
  created_by_id          String?   @db.Uuid
  updated_at             DateTime  @updatedAt
  updated_by_id          String?   @db.Uuid
  deleted_at             DateTime?
  deleted_by_id          String?   @db.Uuid

  project       pmo_projects      @relation(fields: [project_id], references: [id], onDelete: Cascade)
  contract_info pmo_contract_info @relation(fields: [contract_info_id], references: [id], onDelete: Cascade)

  @@index([project_id])
  @@index([contract_info_id])
}

model pmo_bidbond_info {
  id              String    @id @default(uuid()) @db.Uuid
  project_id      String    @db.Uuid
  guarantee_asset String
  bidbond_payer   String
  bidbond_value   Float
  start_date      DateTime  @db.Date
  end_date        DateTime  @db.Date
  duration_month  Int
  duration_year   Int
  fee             Float
  created_at      DateTime  @default(now())
  created_by_id   String?   @db.Uuid
  updated_at      DateTime  @updatedAt
  updated_by_id   String?   @db.Uuid
  deleted_at      DateTime?
  deleted_by_id   String?   @db.Uuid

  project                   pmo_projects                @relation(fields: [project_id], references: [id], onDelete: Cascade)
  pmo_bidbond_info_versions pmo_bidbond_info_versions[]

  @@index([project_id])
}

model pmo_bidbond_info_versions {
  id              String    @id @default(uuid()) @db.Uuid
  bidbond_info_id String    @db.Uuid
  project_id      String    @db.Uuid
  guarantee_asset String
  bidbond_payer   String
  bidbond_value   Float
  start_date      DateTime  @db.Date
  end_date        DateTime  @db.Date
  duration_month  Int
  duration_year   Int
  fee             Float
  created_at      DateTime  @default(now())
  created_by_id   String?   @db.Uuid
  updated_at      DateTime  @updatedAt
  updated_by_id   String?   @db.Uuid
  deleted_at      DateTime?
  deleted_by_id   String?   @db.Uuid

  project      pmo_projects     @relation(fields: [project_id], references: [id], onDelete: Cascade)
  bidbond_info pmo_bidbond_info @relation(fields: [bidbond_info_id], references: [id], onDelete: Cascade)

  @@index([project_id])
  @@index([bidbond_info_id])
}

model pmo_lg_info {
  id            String    @id @default(uuid()) @db.Uuid
  project_id    String    @db.Uuid
  value         Float
  start_date    DateTime  @db.Date
  end_date      DateTime  @db.Date
  fee           Float
  interest      Float
  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  project              pmo_projects           @relation(fields: [project_id], references: [id], onDelete: Cascade)
  pmo_lg_info_versions pmo_lg_info_versions[]

  @@index([project_id])
}

model pmo_lg_info_versions {
  id            String    @id @default(uuid()) @db.Uuid
  lg_info_id    String    @db.Uuid
  project_id    String    @db.Uuid
  value         Float
  start_date    DateTime  @db.Date
  end_date      DateTime  @db.Date
  fee           Float
  interest      Float
  created_at    DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at    DateTime  @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at    DateTime?
  deleted_by_id String?   @db.Uuid

  project pmo_projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
  lg_info pmo_lg_info  @relation(fields: [lg_info_id], references: [id], onDelete: Cascade)

  @@index([project_id])
  @@index([lg_info_id])
}

model pmo_vendor_items {
  id                   String    @id @default(uuid()) @db.Uuid
  project_id           String    @db.Uuid
  vendor_name          String
  item_name            String
  item_detail          String
  deliver_duration_day Int
  is_tor               Boolean
  is_implementation    Boolean
  is_training          Boolean
  is_user_manual       Boolean
  created_at           DateTime  @default(now())
  created_by_id        String?   @db.Uuid
  updated_at           DateTime  @updatedAt
  updated_by_id        String?   @db.Uuid
  deleted_at           DateTime?
  deleted_by_id        String?   @db.Uuid

  project pmo_projects @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@index([project_id])
}
