package requests

import (
	core "gitlab.finema.co/finema/idin-core"
)

type PMOVendorItemCreate struct {
	core.BaseValidator
	VendorName         *string `json:"vendor_name"`
	ItemName           *string `json:"item_name"`
	ItemDetail         *string `json:"item_detail"`
	DeliverDurationDay *int64  `json:"deliver_duration_day"`
	IsTor              *bool   `json:"is_tor"`
	IsImplementation   *bool   `json:"is_implementation"`
	IsTraining         *bool   `json:"is_training"`
	IsUserManual       *bool   `json:"is_user_manual"`
}

func (r *PMOVendorItemCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Vendor<PERSON>ame, "vendor_name"))
	// r.Must(r.IsStrRequired(r.ItemName, "item_name"))
	// r.Must(r.IsStrRequired(r.Item<PERSON>eta<PERSON>, "item_detail"))
	// r.Must(r.IsRequired(r.DeliverDurationDay, "deliver_duration_day"))
	// r.Must(r.IsBoolRequired(r.<PERSON>or, "is_tor"))
	// r.Must(r.IsBoolRequired(r.IsImplementation, "is_implementation"))
	// r.Must(r.IsBoolRequired(r.IsTraining, "is_training"))
	// r.Must(r.IsBoolRequired(r.IsUserManual, "is_user_manual"))

	return r.Error()
}
