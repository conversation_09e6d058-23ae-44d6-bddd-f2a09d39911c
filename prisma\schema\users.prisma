model users {
  id           String  @id @default(uuid()) @db.Uuid
  email        String  @unique
  full_name    String?
  display_name String?
  position     String?
  team_code    String?
  avatar_url   String?
  company      String?
  slack_id     String? @unique
  is_active    Boolean @default(true)

  joined_date DateTime? @db.Date
  created_at  DateTime  @default(now())
  updated_at  DateTime  @default(now()) @updatedAt

  // Relations
  access_tokens        user_tokens[]
  timesheets           timesheets[]
  team                 teams?                 @relation(fields: [team_code], references: [code], onDelete: SetNull)
  checkins             checkins[]
  user_access_level    user_access_levels[]
  files                files[]
  pmo_collaborators    pmo_collaborators[]
  pmo_comments         pmo_comments[]
  pmo_comment_versions pmo_comment_versions[]
  pmo_checklist_items  pmo_checklist_items[]

  @@index([email, team_code])
}
