package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/timesheet/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/timesheet/repositories"
	userRepositories "gitlab.finema.co/finema/finework/finework-api/modules/user/repositories"
	"gitlab.finema.co/finema/finework/finework-api/views"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm"
)

type ITimesheetService interface {
	Create(input *dto.TimesheetCreatePayload) ([]models.Timesheet, core.IError)
	Update(id string, input *dto.TimesheetUpdatePayload) (*models.Timesheet, core.IError)
	Find(id string) (*models.Timesheet, core.IError)
	Pagination(pageOptions *core.PageOptions, options *dto.TimesheetPaginationOptions) (*repository.Pagination[models.Timesheet], core.IError)
	SummaryReport(options *dto.TimesheetSummaryReportOptions) ([]views.TimesheetSummaryReportView, core.IError)
	Delete(id string) core.IError
}

type timesheetService struct {
	ctx core.IContext
}

func (s timesheetService) Create(input *dto.TimesheetCreatePayload) ([]models.Timesheet, core.IError) {
	timesheets := []models.Timesheet{}
	for _, item := range input.Items {
		var leaveType *models.CheckinLeaveType
		if item.LeaveType != nil {
			leaveTypeValue := models.CheckinLeaveType(utils.ToNonPointer(item.LeaveType))
			leaveType = &leaveTypeValue
		}

		timesheet := models.Timesheet{
			BaseModelHardDelete: models.NewBaseModelHardDelete(),
			UserID:              input.UserID,
			SgaID:               item.SgaID,
			ProjectCode:         item.ProjectCode,
			Timing:              item.Timing,
			Type:                models.TimesheetType(item.Type),
			LeaveType:           leaveType,
			Description:         item.Description,
			Date:                item.Date,
			ProjectID:           item.ProjectID,
			ProjectName:         item.ProjectName,
			SgaName:             item.SgaName,
		}
		timesheets = append(timesheets, timesheet)
	}

	ierr := repositories.Timesheet(s.ctx).Create(timesheets)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return timesheets, nil
}

func (s timesheetService) Update(id string, input *dto.TimesheetUpdatePayload) (*models.Timesheet, core.IError) {
	timesheet, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	if input.SgaID != nil {
		timesheet.SgaID = input.SgaID
	}
	if input.SgaName != nil {
		timesheet.SgaName = input.SgaName
	}
	if input.ProjectCode != nil {
		timesheet.ProjectCode = input.ProjectCode
	}
	if input.ProjectID != nil {
		timesheet.ProjectID = input.ProjectID
	}
	if input.ProjectName != nil {
		timesheet.ProjectName = input.ProjectName
	}
	if input.Timing != 0 {
		timesheet.Timing = input.Timing
	}
	if input.Type != "" {
		timesheet.Type = models.TimesheetType(input.Type)
	}
	if input.LeaveType != nil {
		timesheet.LeaveType = utils.ToPointer(models.CheckinLeaveType(utils.ToNonPointer(input.LeaveType)))
	}
	if input.Description != nil {
		timesheet.Description = utils.ToPointer(*input.Description)
	}
	if input.Date != "" {
		timesheet.Date = input.Date
	}

	ierr = repositories.Timesheet(s.ctx).Where("id = ?", id).Updates(timesheet)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(timesheet.ID)
}

func (s timesheetService) Find(id string) (*models.Timesheet, core.IError) {
	return repositories.Timesheet(s.ctx, repositories.TimesheetWithAllRelation()).FindOne("id = ?", id)
}

func (s timesheetService) Pagination(pageOptions *core.PageOptions, options *dto.TimesheetPaginationOptions) (*repository.Pagination[models.Timesheet], core.IError) {
	return repositories.Timesheet(
		s.ctx,
		repositories.TimesheetWithAllRelation(),
		repositories.TimesheetWithUser(options.UserID),
		repositories.TimesheetWithDateRange(options.StartDate, options.EndDate),
		repositories.TimesheetWithSgaId(options.SgaID),
		repositories.TimesheetWithProjectCode(options.ProjectCode),
		repositories.TimesheetWithType(options.Type),
		repositories.TimesheetWithTeamCode(options.TeamCode),
		repositories.TimesheetOrderBy(pageOptions)).
		Pagination(pageOptions)
}

func (s timesheetService) SummaryReport(options *dto.TimesheetSummaryReportOptions) ([]views.TimesheetSummaryReportView, core.IError) {
	users, ierr := userRepositories.User(s.ctx,
		userRepositories.UserWithTeam(options.TeamCode),
		userRepositories.UserWithTimeSheetDateRange(options.StartDate, options.EndDate),
	).
		Preload("Timesheets", func(db *gorm.DB) *gorm.DB {
			// Filter timesheets by date range if provided
			if options.StartDate != nil && options.EndDate != nil {
				return db.Where("DATE(date) BETWEEN ? AND ?", *options.StartDate, *options.EndDate)
			} else if options.StartDate != nil {
				return db.Where("DATE(date) >= ?", *options.StartDate)
			} else if options.EndDate != nil {
				return db.Where("DATE(date) <= ?", *options.EndDate)
			}
			return db
		}).
		Preload("Team").
		FindAll()
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	res := make([]views.TimesheetSummaryReportView, len(users))

	for i, user := range users {
		// Calculate total timing for the user
		var totalTiming float64
		var totalProjectTiming float64
		var totalLeaveTiming float64
		var totalSgaTiming float64
		var totalInternalTiming float64
		var totalExternalTiming float64
		var totalOtTiming float64

		// Use map to aggregate project timings by unique key
		projectTimingMap := make(map[string]*views.TimesheetSummaryReportProjectTiming)

		for _, timesheet := range user.Timesheets {
			totalTiming += timesheet.Timing

			// Calculate timing by type
			switch timesheet.Type {
			case models.TimesheetTypeProject:
				totalProjectTiming += timesheet.Timing
			case models.TimesheetTypeLeave:
				totalLeaveTiming += timesheet.Timing
			case models.TimesheetTypeSga:
				totalSgaTiming += timesheet.Timing
			case models.TimesheetTypeInternal:
				totalInternalTiming += timesheet.Timing
			case models.TimesheetTypeExternal:
				totalExternalTiming += timesheet.Timing
			case models.TimesheetTypeOt:
				totalOtTiming += timesheet.Timing
			}

			// Create unique key for grouping based on type and identifier
			var key string
			var projectCode *string
			var projectName *string
			var sgaName *string

			switch timesheet.Type {
			case models.TimesheetTypeProject:
				projectCode = timesheet.ProjectCode
				projectName = timesheet.ProjectName
				if projectCode != nil {
					key = *projectCode + "|" + string(timesheet.Type)
				} else {
					key = "nil|" + string(timesheet.Type)
				}
			case models.TimesheetTypeSga:
				sgaName = timesheet.SgaName
				if timesheet.SgaID != nil {
					key = *timesheet.SgaID + "|" + string(timesheet.Type)
				} else {
					key = "nil|" + string(timesheet.Type)
				}
			case models.TimesheetTypeOt:
				// Separate OT by project like PROJECT type
				projectCode = timesheet.ProjectCode
				projectName = timesheet.ProjectName
				if projectCode != nil {
					key = *projectCode + "|" + string(timesheet.Type)
				} else {
					key = "nil|" + string(timesheet.Type)
				}
			default:
				// For LEAVE, INTERNAL, EXTERNAL types
				key = "nil|" + string(timesheet.Type)
			}

			// Check if this combination already exists in map
			if existingTiming, exists := projectTimingMap[key]; exists {
				// Add timing to existing entry
				existingTiming.TotalTiming += timesheet.Timing
			} else {
				// Create new entry
				projectTimingMap[key] = &views.TimesheetSummaryReportProjectTiming{
					ProjectCode: projectCode,
					ProjectName: projectName,
					SgaName:     sgaName,
					Type:        string(timesheet.Type),
					TotalTiming: timesheet.Timing,
				}
			}
		}

		// Convert map to slice
		timings := make([]views.TimesheetSummaryReportProjectTiming, 0, len(projectTimingMap))
		for _, timing := range projectTimingMap {
			timings = append(timings, *timing)
		}

		res[i] = views.TimesheetSummaryReportView{
			User:                &user,
			TotalTiming:         totalTiming,
			TotalProjectTiming:  totalProjectTiming,
			TotalLeaveTiming:    totalLeaveTiming,
			TotalSgaTiming:      totalSgaTiming,
			TotalInternalTiming: totalInternalTiming,
			TotalExternalTiming: totalExternalTiming,
			TotalOtTiming:       totalOtTiming,
			Timings:             timings,
		}

	}

	return res, nil
}

func (s timesheetService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.Timesheet(s.ctx).Delete("id = ?", id)
}

func NewTimesheetService(ctx core.IContext) ITimesheetService {
	return &timesheetService{ctx: ctx}
}
