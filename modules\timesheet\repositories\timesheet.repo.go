package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gorm.io/gorm/clause"
)

var Timesheet = repository.Make[models.Timesheet]()

func TimesheetOrderBy(pageOptions *core.PageOptions) repository.Option[models.Timesheet] {
	return func(c repository.IRepository[models.Timesheet]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func TimesheetWithUser(id *string) repository.Option[models.Timesheet] {
	return func(c repository.IRepository[models.Timesheet]) {
		if id == nil {
			return
		}
		c.Where("user_id = ?", id)
	}
}

func TimesheetWithProjectCode(projectCode *string) repository.Option[models.Timesheet] {
	return func(c repository.IRepository[models.Timesheet]) {
		if projectCode == nil {
			return
		}
		c.Where("project_code = ?", projectCode)
	}
}

func TimesheetWithSgaId(sgaID *string) repository.Option[models.Timesheet] {
	return func(c repository.IRepository[models.Timesheet]) {
		if sgaID == nil {
			return
		}
		c.Where("sga_id = ?", sgaID)
	}
}

func TimesheetWithType(tType *string) repository.Option[models.Timesheet] {
	return func(c repository.IRepository[models.Timesheet]) {
		if tType == nil {
			return
		}
		c.Where("type = ?", tType)
	}
}

func TimesheetWithKeywordType(keyWord *string) repository.Option[models.Timesheet] {
	return func(c repository.IRepository[models.Timesheet]) {
		if keyWord == nil {
			return
		}
		c.Where("project_code = ? OR sga_id = ? OR type = ?", keyWord, keyWord, keyWord)
	}
}

func TimesheetWithTeamCode(teamCode []string) repository.Option[models.Timesheet] {
	return func(c repository.IRepository[models.Timesheet]) {
		if len(teamCode) == 0 {
			return
		}

		filteredCode := []string{}
		for _, c := range teamCode {
			if c != "" {
				filteredCode = append(filteredCode, c)
			}
		}
		if len(filteredCode) == 0 {
			return
		}

		c.Joins("JOIN users ON users.id = timesheets.user_id").Where("users.team_code IN ?", filteredCode)
	}
}

func TimesheetWithDateRange(startDate *string, endDate *string) repository.Option[models.Timesheet] {
	return func(c repository.IRepository[models.Timesheet]) {
		if startDate != nil && endDate != nil {
			c.Where("DATE(date) BETWEEN ? AND ?", startDate, endDate)
		} else if startDate != nil {
			c.Where("DATE(date) >= ?", startDate)
		} else if endDate != nil {
			c.Where("DATE(date) <= ?", endDate)
		}
	}
}
func TimesheetWithAllRelation() repository.Option[models.Timesheet] {
	return func(c repository.IRepository[models.Timesheet]) {
		c.Preload(clause.Associations).Preload("User.Team")
	}
}
