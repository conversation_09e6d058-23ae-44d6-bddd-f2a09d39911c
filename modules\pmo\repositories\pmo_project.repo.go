package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var PMOProject = repository.Make[models.PMOProject]()

func PMOProjectOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOProjectWithSearch(q string) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"

		// Enhanced search across PMO project fields and related project fields
		c.Joins("LEFT JOIN projects ON projects.id = pmo_projects.project_id").
			Where(`
				pmo_projects.name ILIKE ?
				OR pmo_projects.slug ILIKE ?
				OR pmo_projects.email ILIKE ?
				OR projects.name ILIKE ?
				OR projects.code ILIKE ?
				OR projects.description ILIKE ?
				OR array_to_string(pmo_projects.tags, ' ') ILIKE ?
			`, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm)
	}
}

func PMOProjectWithStatus(status string) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if status == "" {
			return
		}
		c.Where("status = ?", status)
	}
}

func PMOProjectWithRelations(includeAll bool) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		c.Preload("Project").Preload("CreatedBy").Preload("UpdatedBy").Preload("ContractInfo")
		if includeAll {
			c.Preload("BiddingInfo").
				Preload("Collaborators.User").
				Preload("BudgetInfo").
				Preload("BidbondInfo").
				Preload("LGInfo").
				Preload("Remarks")
		}
	}
}

func PMOProjectWithCurrentPermission(userID string) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if userID == "" {
			return
		}
		c.Preload("Permission", "user_id = ?", userID)
	}
}

func PMOProjectByOwnerPermission(userID string) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if userID == "" {
			return
		}

		// Use a more efficient approach that works better with search functionality
		// Check if user has SUPER access level for PMO - if so, they can see all projects
		c.Where(`
			EXISTS (
				SELECT 1 FROM user_access_levels ual
				WHERE ual.user_id = ?
				AND ual.pmo IN ('SUPER')
			)
			OR EXISTS (
				SELECT 1 FROM pmo_collaborators pc
				WHERE pc.project_id = pmo_projects.id
				AND pc.user_id = ? AND pc.deleted_at IS NULL
			)
			OR pmo_projects.created_by_id = ?
		`, userID, userID, userID)
	}
}
