package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOProjectChecklistItemUpdate struct {
	core.BaseValidator
	Detail     *string `json:"detail"`
	IsChecked  *bool   `json:"is_checked"`
	AssigneeID *string `json:"assignee_id"`
}

func (r *PMOProjectChecklistItemUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsExists(ctx, r.AssigneeID, models.User{}.TableName(), "id", "assignee_id"))

	return r.Error()
}
