# Test PMO Collaborator Main Person Constraint
# This file tests the constraint that only one person can be marked as "main" for each tab

### Variables
@baseUrl = http://localhost:8080
@projectId = your-project-id-here
@user1Id = user-1-id-here
@user2Id = user-2-id-here
@token = your-auth-token-here

### 1. <PERSON>reate first collaborator with sales_main = true
POST {{baseUrl}}/pmo/projects/{{projectId}}/collaborators
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "user_id": "{{user1Id}}",
  "sales_permission": "MODIFY",
  "sales_main": true,
  "confidential_permission": "READONLY",
  "confidential_main": false
}

### 2. <PERSON>reate second collaborator with sales_main = true (should unset the first one)
POST {{baseUrl}}/pmo/projects/{{projectId}}/collaborators
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "user_id": "{{user2Id}}",
  "sales_permission": "MODIFY",
  "sales_main": true,
  "confidential_permission": "READON<PERSON><PERSON>",
  "confidential_main": false
}

### 3. Get all collaborators to verify only user2 has sales_main = true
GET {{baseUrl}}/pmo/projects/{{projectId}}/collaborators
Authorization: Bearer {{token}}

### 4. Test updating existing collaborator to set confidential_main = true
PUT {{baseUrl}}/pmo/projects/{{projectId}}/collaborators/{{collaborator1Id}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "confidential_main": true
}

### 5. Test updating another collaborator to set confidential_main = true (should unset the previous one)
PUT {{baseUrl}}/pmo/projects/{{projectId}}/collaborators/{{collaborator2Id}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "confidential_main": true
}

### 6. Get all collaborators again to verify constraint is working
GET {{baseUrl}}/pmo/projects/{{projectId}}/collaborators
Authorization: Bearer {{token}}

### 7. Test multiple main flags in single request
POST {{baseUrl}}/pmo/projects/{{projectId}}/collaborators
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "user_id": "{{user3Id}}",
  "sales_permission": "MODIFY",
  "sales_main": true,
  "presales_permission": "MODIFY", 
  "presales_main": true,
  "bidding_permission": "READONLY",
  "bidding_main": true,
  "pmo_permission": "MODIFY",
  "pmo_main": true,
  "bizco_permission": "READONLY",
  "bizco_main": true
}

### 8. Final check - get all collaborators to see the main person assignments
GET {{baseUrl}}/pmo/projects/{{projectId}}/collaborators
Authorization: Bearer {{token}}
